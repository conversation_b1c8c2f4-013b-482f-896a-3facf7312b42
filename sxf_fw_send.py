# 使用logger发送syslog日志
# 例如: logger -p syslog.info "日志类型:WEB应用防护, 策略名称:waf1, 规则ID:0, 源IP:*************, 源端口:7224, 目的IP:**************, 目的端口:80, 攻击类型:跨站请求伪造, 严重级别: 低, 系统动作:允许, URL:http://www.sangfor.com/waf.jsp"
import socket
import time

def send_syslog(message, server_address='*************', server_port=514):
    # 创建UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    # 构造syslog消息
    # 构造syslog消息
    severity = 2  # 2 表示 "危险" 级别
    facility = 1  # 1 表示 "user" facility
    priority = (facility * 8) + severity

    #timestamp = time.strftime("%b %d %H:%M:%S", time.strptime('2024-10-24 10:50:00', '%Y-%m-%d %H:%M:%S'))
    timestamp = time.strftime("%b %d %H:%M:%S", time.localtime())
    hostname = socket.gethostname()
    syslog_message = f'<{priority}>{timestamp} {hostname} {message}'
    
    print(syslog_message)
    # 发送消息
    sock.sendto(syslog_message.encode('utf-8'), (server_address, server_port))
    
    # 关闭socket
    sock.close()


# syslog 消息内容
messages = [
    '日志类型:WEB应用防护, 策略名称:waf1, 规则ID:0, 源IP:*************, 源端口:7224, 目的IP:**************, 目的端口:80, 攻击类型:跨站请求伪造, 严重级别:低, 系统动作:允许, URL:http://www.sangfor.com/waf.jsp',
    '日志类型:IPS防护日志, 策略名称:ips2, 漏洞ID:1265, 漏洞名称:WebCalendar本地文件包含和PHP代码注入漏洞, 源IP:*************, 源端口:80, 目的IP:*************00, 目的端口:80, 协议:tcp, 攻击类型:web漏洞攻击, 严重等级:高, 动作:拒绝',
    '日志类型:DOS攻击日志, 策略名称:dos2, 源IP:*************, 目的IP:**************, 攻击方向:外网, 攻击类型:IP宽松源路由选项报文, 严重等级:低, 系统动作:拒绝',
    '日志类型:病毒查杀, 策略名称:virus22, 用户:donnie, 源IP:**********, 源端口:8271, 目的IP:954f:2588:3600::, 目的端口:110, 病毒类型:4160744368, 病毒名:Backdoor.RmtBomb.2, 应用名称:邮件, 严重等级:高, 系统动作:拒绝, URL:http://**************/repro/adobe_pdf_embedded_exe_nojs.pdf',
    '日志类型:WEB威胁, 策略名称:web_threat, 用户:b, 源IP:***********, 目的IP:***********, 应用名称:p2p, 系统动作:拒绝, URL:http://***********/repro/ss',
    '日志类型:网站访问, 策略名称:web_access, 用户:(null), 源IP:*************, 目的IP:*************00, 应用名称:IT相关, 系统动作:被记录, URL:http://www.shenxinfu.com/simple.php',
    '日志类型:服务控制或应用控制, 策略名称:-, 用户:(null), 源IP:********, 源端口:1025, 目的IP:************, 目的端口:621, 应用类型:Other, 应用名称:Other, 系统动作:允许',
    '日志类型:系统操作, 用户:admin, IP地址:**************, 操作对象:启用禁用, 操作类型:启用, 描述:OSPF 启用 成功',
    '日志类型:用户认证, 用户:*************99, IP地址:*************99, 操作对象:注销, 登录时间:2016-05-25 10:55:54, 登录时长:1569, 注销时间:11:22:03',
    '日志类型:僵尸网络日志, 策略名称:-, 特征ID:34014989, 源IP:***********, 源端口:4720, 目的IP:**************, 目的端口:80, 攻击类型:僵尸网络, 严重级别:低, 系统动作:拒绝, URL:www.audi_log.org/test.html',
    '日志类型:僵尸网络日志, 规则ID:23636, 源IP:************, 源端口:80, 目的IP:***********, 目的端口:80, 风险类型:1, 严重级别:高, 协议:tcp, URL:www.audi_log.org/pvs',
    '日志类型:SSL VPN用户行为日志, 用户:wjj, IP地址:**************, 操作对象:SSL VPN, 操作:登录, 时间:2016-05-25 12:45:59, 描述:登录成功',
    '日志类型:邮件安全日志, 策略名称:-, 规则ID:6646, 源IP:************, 源端口:25, 目的IP:**************, 目的端口:25, 攻击类型:撞库攻击, 严重级别:高, 系统动作:拒绝',
    '日志类型:流量审计, 应用类型:SOAP, 用户名/主机:***********, 上行流量(KB):29485, 下行流量(KB):49433, 总流量(KB):78918',
    '日志类型:本机安全, 策略名称:-, 源IP:***********, 源端口:1025, 目的端口:762, 应用类型:SSL, 应用名称:SSL_HELLO, 系统动作:拒绝',
    '日志类型:高可用性, 网关序列号:62B7B65B, 设备名称:NGAF, IP地址:**********, VRID:100, 告警类型:双机切换, 当前状态:故障, 原因描述:本端监视口eth2故障',
    '时间:2024-10-22 13:38:54, 时间戳: 1729577911, 目的IP:**************, 目的端口:80, 应用类型:Other, 应用名称:Other, 系统动作:允许',
]
# 发送 syslog 消息
for message in messages:
    send_syslog(message)
    break